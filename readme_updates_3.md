I want to make the following changes to the chat-back app:

New Panel "Missed RFQ Messages"
-------------------------------
    - Add a new panel called "Missed RFQ Messages"
    - This panel should be on the right hand side of the screen directly under the chat-panel (Chat Messages)
    - The panel should only take up ~ 10 perent of the vertical space
    - The panel should be scrollable

Functionality
-------------
    - there should be ability to add a new entry to the panel which consist of two message_id fields corresponding to  message_id s in the chat panel
        RFQ_start: int
        RFQ_end: int
    - these entrys should be stored to the database in a new json called "missed_rfqs.json" with its own schema
    - the panel should display all entries in a table format (frozen header row) with the following columns:
        - start message_id
        - end message_id
    - when a row is clicked, the chat panel should scroll to the RFQ_start message_id
    - when a row is clicked, the corresponding rows between the start message_id and end message_id should be highlighted and bolded

Applying the Changes
--------------------
    - I will apply these changes to the chat-back app
    - I will ensure that the new panel is responsive and fits well within the existing layout
    - I will respect the existing design and functionality of the chat-back app and respect the existing code structure / conventtions and looks and feel
