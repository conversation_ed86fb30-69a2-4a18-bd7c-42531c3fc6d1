Chat-back Application
---------------------

I want you to create a simple single page web application to display and visualise financial market chat and rfq data.

The purpose of the app is to compare extracted RFQs from a different source to the chat messages to see if the RFQs are correctly \
extracted from the chat messages.

The application should allow the user to update the extracted RFQs and save them back to an updated database repository.

The application should contain the following:

RFQs Display - top panel:
-------------------------

    A scrollable  list of RFQs with the followig fields displayed:
        - RFQ_startTime:  start time in UTC in database is stored as YYYY-MM-DDTHH:MM:SS.SSSZ
        - sales:  sales person that initiated the RFQ
        - trader: trader that responded to the RFQ
        - customer: customer that the RFQ is for
        - currency_pair:  currency pair field from the database
        - amount:  amount field from the database (currently a string as not parsed)
        - base:  base currency field from the database
        - direction:  direction field ususally LHS, RHS or TWO_WAY (string)
        - dates:  dates field from the database (currently as a string as not parced)
        - price:  field from the database (currently a string as not parsed)
        - outcome:  outcome field from the database (done, passed, dealt away etc)
        - confidence:  confidence field from the database (float between 0 and 1 - two decimal places)
        - notes:  notes field from the database

    the following for each RFQ should be hidden - but are used for selection into the chat display window

        - RFQ_start: integer
        - RFQ_end: integer
        - message_ids: array of integers that form the chat from which the RFQ was extracted


Chat Display - bottom panel:
----------------------------

    A scrollable list of chat messages with the followig fields displayed:

        - eventTime: time in UTC in database is stored as YYYY-MM-DDTHH:MM:SS.SSSZ
        - desk:  desk that the user belongs to (SALES or TRADING) 
        - loginName:  user that sent the message
        - content:  content of the message

    the following field should be hidden and is used for selection highlighting

        - message_id (integer)

    sales and trading messages should be displayed in different colours without being distracting

Functionality:
--------------

    - RFQ Display
        1. When a user clicks on an RFQ in the top panel it should
            - SCROLL to the place in the list where the first message begins - this will key on RFQ_StartTime == eventTime
            - HIGHLIGHT (while still being clearly readable) those messages in the chat display window between RFQ_startTime and RFQ_endTime where 
                - RFQ_startTime <= eventTime <= RFQ_endTime and
                - RFQ_start <= message_id <= RFQ_end and
                - message_id is in the array of message_ids 

        2. the user should be able to sort the RFQs by "startTime" and "confidence"
        3. the user should be able to filter by "sales", "customer", "currency_pair", "outcome"
        4. for "confidence" there should be ability to filter by confidence > 0.9, confidence > 0.8, confidence > 0.7 etc

    - Chat Display
        1. the user should be able to scroll through the chat messages
        2. the user should be able to search for a specific message by content

    - Update RFQ requirements
        1. the user should be able to update the following inplace easily by selecting an edit toggle on the row.
            - customer
            - currency_pair
            - amount
            - base
            - direction
            - dates
            - price
            - outcome
            - confidence
        2. the user should be able to add a note to the RFQ if they updated any of the above fields
        3. if edited the row should highlight the updated fields in a different colour 
        4. the user should be able to save the updated RFQ back to a change set.


Database
--------
    The database is two simple json files

    1. db/rfqs.json - this contains the list of rfqs

        an example rfq is

             [
              {
                "RFQ_start": 2,
                "RFQ_end": 6,
                "RFQ_startTime": "2025-01-02T00:37:00.054Z",
                "RFQ_endTime": "2025-01-02T00:42:00.036Z",
                "sales": "sales_1",
                "trader": "trader_1",
                "customer": "customer_1",
                "amount": "340",
                "currency_pair": "AUDUSD",
                "base": "USD",
                "dates": ["30 may", "31 oct"],
                "direction": "TWO_WAY",
                "price": "5.8/6.6",
                "outcome": "passed",
                "confidence": 1.0,
                "notes": "ntg=noting/no trade given",
                "message_ids": [2, 3, 4, 5, 6]
              },
            ]

        note RFQ_start and RFQ_send and message_ids that form the chat from which the RFQ was extracted. \
        they are not unique we align to eventTime

    2. db/chat.json - this contains the list of chat messages

        an example list of chat message content is

        [
            {"desk":"TRADING","loginName":"xxx","message_id":1,"eventTime":"2025-01-01T23:59:00.021Z","content":"yyy"},
            {"desk":"SALES","loginName":"xxx","message_id":2,"eventTime":"2025-01-02T00:37:00.054Z","content":"yyy"},
            {"desk":"TRADING","loginName":"xxx","message_id":3,"eventTime":"2025-01-02T00:39:00.017Z","content":"yyy"},
            {"desk":"TRADING","loginName":"xxx","message_id":4,"eventTime":"2025-01-02T00:40:00.047Z","content":"yyy"},
            {"desk":"SALES","loginName":"xxx","message_id":5,"eventTime":"2025-01-02T00:41:00.034Z","content":"yyy"},
            {"desk":"SALES","loginName":"xxx","message_id":6,"eventTime":"2025-01-02T00:42:00.036Z","content":"yyy"},
            {"desk":"SALES","loginName":"xxx","message_id":7,"eventTime":"2025-01-02T00:49:00.036Z","content":"yyy"},
            {"desk":"SALES","loginName":"xxx","message_id":8,"eventTime":"2025-01-02T00:49:00.043Z","content":"yyy"},
        ]

    you should read from the json files and display the data in the application

    3.  for updated RFQ fields you should write into a new json file called db/updated_rfqs.json

        this tracks the updates that were made by the user separate to the original data and should be applied to the rfq data


Non Functional Requirements:
----------------------------

    - the application should be written using python and the flask framework.
    - lay out the code with clean separation of concerns  database (db) / ui / data classes
    - the database of chats / rfqs should be read from json files provided int he db directoryand not be modified
    - javascript should be used for the UI and data manipulation 
    - use styleheets and templates for the ui
    - I want the UI to have clean / modern look and feel in tyhe style of a financial markets application

