[{"rfq_start": 2, "rfq_start_time": "2025-01-02T00:37:00.054Z", "updated_fields": {"currency_pair": "USDKRW", "confidence": 1}, "notes": "ntg=noting/no trade given", "timestamp": "2025-08-03T00:34:27.639799Z"}, {"rfq_start": 14, "rfq_start_time": "2025-01-02T01:10:00.057Z", "updated_fields": {"customer": "customer_4", "currency_pair": "NZDUSD", "confidence": 0.95}, "notes": "sales says 'done rhs'", "timestamp": "2025-08-03T00:14:52.814851Z"}, {"rfq_start": 35, "rfq_start_time": "2025-01-02T03:07:00.034Z", "updated_fields": {"customer": "customer_1", "currency_pair": "AUDUSD", "amount": 85, "base": "USD", "direction": "RHS", "dates": ["TOD", "3 Feb 25"], "price": 0.2444, "outcome": "done", "confidence": 0.9, "notes": "customer not explicit, likely recurring"}, "notes": "customer not explicit, likely recurring", "timestamp": "2025-08-07T23:22:41.421807+00:00"}, {"rfq_start": 2, "rfq_start_time": "2025-01-02T00:37:00.054Z", "updated_fields": {"customer": "customer_1", "currency_pair": "USDKRW", "amount": 340, "base": "USD", "direction": "TWO_WAY", "dates": ["30 may", "31 oct"], "price": "5.8/6.7", "outcome": "passed", "confidence": 1, "notes": "ntg=noting/no trade given"}, "notes": "ntg=noting/no trade given", "timestamp": "2025-08-07T23:22:50.907957+00:00"}]