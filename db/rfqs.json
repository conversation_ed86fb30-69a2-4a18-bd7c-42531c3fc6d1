[{"RFQ_start": 2, "RFQ_end": 6, "RFQ_startTime": "2025-01-02T00:37:00.054Z", "RFQ_endTime": "2025-01-02T00:42:00.036Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "340", "currency_pair": "AUDUSD", "base": "USD", "dates": ["30 may", "31 oct"], "direction": "TWO_WAY", "price": "5.8/6.6", "outcome": "passed", "confidence": 1.0, "notes": "ntg=noting/no trade given", "message_ids": [2, 3, 4, 5, 6]}, {"RFQ_start": 8, "RFQ_end": 12, "RFQ_startTime": "2025-01-02T00:49:00.043Z", "RFQ_endTime": "2025-01-02T00:51:00.004Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "170", "currency_pair": "NZDUSD", "base": "NZD", "dates": ["TN"], "direction": "LHS", "price": "0.1", "outcome": "passed", "confidence": 1.0, "notes": "", "message_ids": [8, 9, 10, 11, 12]}, {"RFQ_start": 14, "RFQ_end": 23, "RFQ_startTime": "2025-01-02T01:10:00.057Z", "RFQ_endTime": "2025-01-02T01:16:00.059Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "340", "currency_pair": "AUDUSD", "base": "USD", "dates": ["30/5", "31/10"], "direction": "TWO_WAY", "price": "5.8/6.6", "outcome": "done", "confidence": 0.95, "notes": "sales says 'done rhs'", "message_ids": [14, 15, 16, 17, 18, 19, 20, 21, 22, 23]}, {"RFQ_start": 35, "RFQ_end": 41, "RFQ_startTime": "2025-01-02T03:07:00.034Z", "RFQ_endTime": "2025-01-02T03:17:00.048Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "85", "currency_pair": "AUDUSD", "base": "USD", "dates": ["TOD", "3 Feb 25"], "direction": "RHS", "price": "0.25", "outcome": "done", "confidence": 0.9, "notes": "customer not explicit, likely recurring", "message_ids": [35, 36, 37, 38, 39, 40, 41]}, {"RFQ_start": 45, "RFQ_end": 48, "RFQ_startTime": "2025-01-02T04:56:00.014Z", "RFQ_endTime": "2025-01-02T04:59:00.009Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "15", "currency_pair": "USDNOK", "base": "USD", "dates": ["4 Feb"], "direction": "LHS", "price": "2.5", "outcome": "passed", "confidence": 0.85, "notes": "no cust in chat, passed", "message_ids": [45, 46, 47, 48]}, {"RFQ_start": 58, "RFQ_end": 67, "RFQ_startTime": "2025-01-02T08:05:00.020Z", "RFQ_endTime": "2025-01-02T08:07:00.040Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "380", "currency_pair": "EURUSD", "base": "EUR", "dates": ["9/1", "9/4"], "direction": "TWO_WAY", "price": "42.95/43.15", "outcome": "passed", "confidence": 1.0, "notes": "trader tried to tighten for client", "message_ids": [58, 59, 60, 61, 62, 63, 64, 65, 66, 67]}, {"RFQ_start": 71, "RFQ_end": 91, "RFQ_startTime": "2025-01-02T08:40:00.056Z", "RFQ_endTime": "2025-01-02T08:46:00.027Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "1,750", "currency_pair": "AUDUSD", "base": "AUD", "dates": ["03 Jan", "04 Feb"], "direction": "RHS", "price": "0.18/0.24", "outcome": "done", "confidence": 0.85, "notes": "RHS/BR AUD, sales books as done", "message_ids": [71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91]}, {"RFQ_start": 124, "RFQ_end": 133, "RFQ_startTime": "2025-01-02T10:08:00.043Z", "RFQ_endTime": "2025-01-02T10:10:00.016Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "195", "currency_pair": "GBPUSD", "base": "GBP", "dates": ["08 Jan", "04 Feb"], "direction": "TWO_WAY", "price": "-2.99/-2.94", "outcome": "passed", "confidence": 0.8, "notes": "", "message_ids": [124, 125, 126, 127, 128, 129, 130, 131, 132, 133]}, {"RFQ_start": 137, "RFQ_end": 152, "RFQ_startTime": "2025-01-02T10:27:00.030Z", "RFQ_endTime": "2025-01-02T10:35:00.032Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "225", "currency_pair": "EURUSD", "base": "EUR", "dates": ["06 Jan 25", "06 Jan 26"], "direction": "LHS", "price": "210.25", "outcome": "done", "confidence": 0.85, "notes": "EUR indicated as base on ticket amend", "message_ids": [137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152]}, {"RFQ_start": 155, "RFQ_end": 164, "RFQ_startTime": "2025-01-02T15:18:00.052Z", "RFQ_endTime": "2025-01-02T15:57:00.020Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "207", "currency_pair": "USDCAD", "base": "CAD", "dates": ["spot", "6 March"], "direction": "LHS", "price": "-32.95", "outcome": "done", "confidence": 0.75, "notes": "price posted at 4pm, follow-up value -32.95", "message_ids": [155, 156, 157, 158, 159, 160, 161, 162, 163, 164]}, {"RFQ_start": 170, "RFQ_end": 182, "RFQ_startTime": "2025-01-02T17:16:00.004Z", "RFQ_endTime": "2025-01-02T17:19:00.050Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "329", "currency_pair": "AUDUSD", "base": "AUD", "dates": ["8 Jan", "4 Feb"], "direction": "RHS", "price": "+0.25", "outcome": "done", "confidence": 1.0, "notes": "", "message_ids": [170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182]}, {"RFQ_start": 195, "RFQ_end": 200, "RFQ_startTime": "2025-01-02T20:29:00.011Z", "RFQ_endTime": "2025-01-02T20:29:00.058Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "727.590895", "currency_pair": "NZDUSD", "base": "NZD", "dates": ["06JAN25", "07JAN25"], "direction": "TWO_WAY", "price": "", "outcome": "", "confidence": 0.5, "notes": "no price, RFQ not completed", "message_ids": [195, 196, 197, 198, 199, 200]}, {"RFQ_start": 5, "RFQ_end": 18, "RFQ_startTime": "2025-01-02T21:41:00.036Z", "RFQ_endTime": "2025-01-02T21:50:00.035Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "67.3", "currency_pair": "AUDUSD", "base": "AUD", "dates": ["03/01", "06/01"], "direction": "LHS", "price": "0.08", "outcome": "done", "confidence": 0.85, "notes": "client not named, clear workflow", "message_ids": [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18]}, {"RFQ_start": 19, "RFQ_end": 42, "RFQ_startTime": "2025-01-02T22:02:00.008Z", "RFQ_endTime": "2025-01-02T22:17:00.001Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "100.253", "currency_pair": "NZDUSD", "base": "NZD", "dates": ["7/1", "14/1"], "direction": "RHS", "price": "0.72", "outcome": "done", "confidence": 0.75, "notes": "extra context for 3w leg in 100.253m NZD, OCO", "message_ids": [19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42]}, {"RFQ_start": 43, "RFQ_end": 45, "RFQ_startTime": "2025-01-02T23:01:00.008Z", "RFQ_endTime": "2025-01-02T23:02:00.026Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "170", "currency_pair": "NZDUSD", "base": "NZD", "dates": ["<PERSON> next"], "direction": "LHS", "price": "", "outcome": "passed", "confidence": 0.8, "notes": "gone, no price quoted prior to pass", "message_ids": [43, 44, 45]}, {"RFQ_start": 46, "RFQ_end": 49, "RFQ_startTime": "2025-01-03T00:13:00.046Z", "RFQ_endTime": "2025-01-03T00:16:00.025Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "631", "currency_pair": "AUDUSD", "base": "USD", "dates": ["9 jan", "9 Apr"], "direction": "TWO_WAY", "price": "0.8/1.4", "outcome": "passed", "confidence": 0.95, "notes": "", "message_ids": [46, 47, 48, 49]}, {"RFQ_start": 50, "RFQ_end": 54, "RFQ_startTime": "2025-01-03T00:23:00.007Z", "RFQ_endTime": "2025-01-03T00:25:00.043Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "355", "currency_pair": "AUDUSD", "base": "USD", "dates": ["10 Jan", "5 Feb"], "direction": "TWO_WAY", "price": "0.12/0.25", "outcome": "dealt away", "confidence": 0.95, "notes": "sales said away", "message_ids": [50, 51, 52, 53, 54]}, {"RFQ_start": 55, "RFQ_end": 58, "RFQ_startTime": "2025-01-03T00:36:00.017Z", "RFQ_endTime": "2025-01-03T00:39:00.011Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "664", "currency_pair": "AUDUSD", "base": "USD", "dates": ["15 Jan", "15 Apr"], "direction": "TWO_WAY", "price": "1.1/1.4", "outcome": "dealt away", "confidence": 0.95, "notes": "sales said away", "message_ids": [55, 56, 57, 58]}, {"RFQ_start": 63, "RFQ_end": 67, "RFQ_startTime": "2025-01-03T01:24:00.019Z", "RFQ_endTime": "2025-01-03T01:28:00.001Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "664", "currency_pair": "AUDUSD", "base": "USD", "dates": ["15 Jan", "15 Apr"], "direction": "TWO_WAY", "price": "1.21-1.41", "outcome": "dealt away", "confidence": 0.95, "notes": "sales said away", "message_ids": [63, 64, 65, 66, 67]}, {"RFQ_start": 69, "RFQ_end": 73, "RFQ_startTime": "2025-01-03T02:19:00.058Z", "RFQ_endTime": "2025-01-03T02:22:00.007Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "631", "currency_pair": "AUDUSD", "base": "USD", "dates": ["9 Jan", "9 Apr"], "direction": "TWO_WAY", "price": "1.05/1.45", "outcome": "dealt away", "confidence": 0.95, "notes": "sales said away", "message_ids": [69, 70, 71, 72, 73]}, {"RFQ_start": 74, "RFQ_end": 83, "RFQ_startTime": "2025-01-03T02:34:00.005Z", "RFQ_endTime": "2025-01-03T02:41:00.003Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "135", "currency_pair": "AUDUSD", "base": "AUD", "dates": ["8/1", "18/6"], "direction": "RHS", "price": "3.8", "outcome": "done", "confidence": 0.85, "notes": "two prices quoted for two directions, done on rhs", "message_ids": [74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84]}, {"RFQ_start": 85, "RFQ_end": 90, "RFQ_startTime": "2025-01-03T02:51:00.022Z", "RFQ_endTime": "2025-01-03T02:55:00.036Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "275", "currency_pair": "NZDUSD", "base": "USD", "dates": ["7/1", "7/4"], "direction": "RHS", "price": "6.53", "outcome": "passed", "confidence": 1.0, "notes": "", "message_ids": [85, 86, 87, 88, 89, 90]}, {"RFQ_start": 91, "RFQ_end": 94, "RFQ_startTime": "2025-01-03T03:13:00.044Z", "RFQ_endTime": "2025-01-03T03:16:00.040Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "690", "currency_pair": "AUDUSD", "base": "AUD", "dates": ["6/1", "7/1"], "direction": "LHS", "price": "0.025", "outcome": "passed", "confidence": 0.8, "notes": "sales called off trade", "message_ids": [91, 92, 93, 94]}, {"RFQ_start": 99, "RFQ_end": 103, "RFQ_startTime": "2025-01-03T04:11:00.011Z", "RFQ_endTime": "2025-01-03T04:17:00.044Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "135", "currency_pair": "AUDUSD", "base": "AUD", "dates": ["6 Jan 24", "13 Jan 26"], "direction": "RHS", "price": "13.35", "outcome": "done", "confidence": 0.85, "notes": "no explicit customer name", "message_ids": [99, 100, 101, 102, 103]}, {"RFQ_start": 107, "RFQ_end": 118, "RFQ_startTime": "2025-01-03T08:01:00.006Z", "RFQ_endTime": "2025-01-03T08:04:00.038Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "72.6", "currency_pair": "USDCAD", "base": "CAD", "dates": ["0601", "0610"], "direction": "RHS", "price": "-153.2", "outcome": "passed", "confidence": 0.95, "notes": "sales said they passed", "message_ids": [107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118]}, {"RFQ_start": 121, "RFQ_end": 129, "RFQ_startTime": "2025-01-03T11:41:00.002Z", "RFQ_endTime": "2025-01-03T11:43:00.044Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "215", "currency_pair": "EURUSD", "base": "EUR", "dates": ["0701", "0602"], "direction": "RHS", "price": "12.10", "outcome": "dealt away", "confidence": 0.95, "notes": "sales: it's away", "message_ids": [121, 122, 123, 124, 125, 126, 127, 128, 129]}, {"RFQ_start": 132, "RFQ_end": 145, "RFQ_startTime": "2025-01-03T11:54:00.044Z", "RFQ_endTime": "2025-01-03T12:04:00.055Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "240", "currency_pair": "AUDUSD", "base": "AUD", "dates": ["7/1", "13/3"], "direction": "TWO_WAY", "price": "0.80/0.85", "outcome": "done", "confidence": 0.7, "notes": "follow-on message says will get reprice for 4pm, treated as one block", "message_ids": [132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145]}, {"RFQ_start": 152, "RFQ_end": 160, "RFQ_startTime": "2025-01-03T12:47:00.044Z", "RFQ_endTime": "2025-01-03T12:52:00.012Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "720", "currency_pair": "NZDUSD", "base": "NZD", "dates": ["07JAN25", "19MAR25"], "direction": "RHS", "price": "4.57", "outcome": "done", "confidence": 1.0, "notes": "", "message_ids": [152, 153, 154, 155, 156, 157, 158, 159, 160]}, {"RFQ_start": 162, "RFQ_end": 166, "RFQ_startTime": "2025-01-03T12:55:00.013Z", "RFQ_endTime": "2025-01-03T12:59:00.055Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "250", "currency_pair": "USDCAD", "base": "USD", "dates": ["2mth"], "direction": "TWO_WAY", "price": "", "outcome": "passed", "confidence": 0.8, "notes": "no price, passed", "message_ids": [162, 163, 164, 165, 166]}, {"RFQ_start": 167, "RFQ_end": 174, "RFQ_startTime": "2025-01-03T14:04:00.006Z", "RFQ_endTime": "2025-01-03T14:06:00.050Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "100", "currency_pair": "USDNOK", "base": "USD", "dates": ["10jan25", "10feb25"], "direction": "TWO_WAY", "price": "7.15", "outcome": "passed", "confidence": 0.9, "notes": "passes after price shown", "message_ids": [167, 168, 169, 170, 171, 172, 173, 174]}, {"RFQ_start": 3, "RFQ_end": 6, "RFQ_startTime": "2025-01-05T21:17:00.042Z", "RFQ_endTime": "2025-01-05T21:19:00.023Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": [{"amount": 10, "dates": ["7/1", "28/1"]}, {"amount": 0.5, "dates": ["7/1", "7/4"]}, {"amount": 16, "dates": ["7/1", "30/4"]}], "currency_pair": "AUDUSD", "base": "AUD", "dates": [["7/1", "28/1"], ["7/1", "7/4"], ["7/1", "30/4"]], "direction": "LHS", "price": [".22", "1.12", "1.82"], "outcome": "passed", "confidence": 0.85, "notes": "multiple tenors in one request; no deal follow-up", "message_ids": [3, 4, 5, 6]}, {"RFQ_start": 7, "RFQ_end": 12, "RFQ_startTime": "2025-01-05T21:25:00.033Z", "RFQ_endTime": "2025-01-05T21:28:00.052Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "98.3", "currency_pair": "NZDUSD", "base": "USD", "dates": ["today", "31/1/25"], "direction": "LHS", "price": "+.72", "outcome": "passed", "confidence": 0.95, "notes": "customer not explicitly given", "message_ids": [7, 8, 9, 10, 11, 12]}, {"RFQ_start": 13, "RFQ_end": 17, "RFQ_startTime": "2025-01-05T22:47:00.033Z", "RFQ_endTime": "2025-01-05T22:49:00.015Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "158", "currency_pair": "NZDUSD", "base": "NZD", "dates": ["8 jan", "3 Feb"], "direction": "LHS", "price": ".87/.97", "outcome": "passed", "confidence": 0.98, "notes": "two-way price quoted, but sales passes", "message_ids": [13, 14, 15, 16, 17]}, {"RFQ_start": 24, "RFQ_end": 26, "RFQ_startTime": "2025-01-05T23:21:00.003Z", "RFQ_endTime": "2025-01-05T23:22:00.023Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "950", "currency_pair": "USDJPY", "base": "USD", "dates": ["9 jan", "9 apr"], "direction": "TWO_WAY", "price": "1.1/1.5", "outcome": "passed", "confidence": 1.0, "notes": "", "message_ids": [24, 25, 26]}, {"RFQ_start": 27, "RFQ_end": 31, "RFQ_startTime": "2025-01-05T23:54:00.005Z", "RFQ_endTime": "2025-01-05T23:55:00.019Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "165", "currency_pair": "NZDUSD", "base": "NZD", "dates": ["T/N"], "direction": "LHS", "price": "0.025", "outcome": "done", "confidence": 1.0, "notes": "", "message_ids": [27, 28, 29, 30, 31]}, {"RFQ_start": 32, "RFQ_end": 35, "RFQ_startTime": "2025-01-05T23:58:00.017Z", "RFQ_endTime": "2025-01-06T00:01:00.031Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "300", "currency_pair": "AUDUSD", "base": "AUD", "dates": ["T/N"], "direction": "RHS", "price": "0.03", "outcome": "passed", "confidence": 1.0, "notes": "", "message_ids": [32, 33, 34, 35]}, {"RFQ_start": 39, "RFQ_end": 44, "RFQ_startTime": "2025-01-06T00:39:00.001Z", "RFQ_endTime": "2025-01-06T00:43:00.032Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "157", "currency_pair": "AUDUSD", "base": "AUD", "dates": ["8 jan", "7 Feb"], "direction": "RHS", "price": "0.3", "outcome": "done", "confidence": 1.0, "notes": "", "message_ids": [39, 40, 41, 42, 43, 44]}, {"RFQ_start": 45, "RFQ_end": 48, "RFQ_startTime": "2025-01-06T03:06:00.052Z", "RFQ_endTime": "2025-01-06T03:08:00.009Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "260", "currency_pair": "NZDUSD", "base": "NZD", "dates": ["1w"], "direction": "LHS", "price": ".20", "outcome": "done", "confidence": 1.0, "notes": "", "message_ids": [45, 46, 47, 48]}, {"RFQ_start": 87, "RFQ_end": 91, "RFQ_startTime": "2025-01-06T15:13:00.027Z", "RFQ_endTime": "2025-01-06T16:08:00.011Z", "sales": "sales_1", "trader": "trader_1", "customer": "customer_1", "amount": "725", "currency_pair": "AUDUSD", "base": "AUD", "dates": ["13jan", "9april"], "direction": "RHS", "price": "+1.15", "outcome": "done", "confidence": 1.0, "notes": "", "message_ids": [87, 88, 89, 90, 91]}]